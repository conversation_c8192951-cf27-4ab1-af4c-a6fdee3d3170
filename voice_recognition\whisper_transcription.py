"""
Whisper High-Accuracy Transcription for STEM-Xpert Robot
OpenAI Whisper implementation for high-accuracy speech recognition
"""

import logging
import time
import tempfile
import os
from typing import Optional, Dict, Any, List
import numpy as np
import whisper
import torch
from .config import VoiceConfig
from .utils import AudioUtils, TextUtils, PerformanceUtils

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WhisperTranscriber:
    """
    OpenAI Whisper-based speech transcription
    Provides high-accuracy multilingual speech recognition
    """
    
    def __init__(self, model_size: str = None, language: str = 'english', device: str = None):
        """
        Initialize Whisper transcriber
        
        Args:
            model_size: Whisper model size (tiny, base, small, medium, large)
            language: Target language for transcription
            device: Device to run model on (cpu, cuda)
        """
        self.language = language
        self.language_code = VoiceConfig.get_language_code(language)
        self.model_size = model_size or VoiceConfig.WHISPER_MODEL
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        
        # Language mapping for Whisper
        self.whisper_language_map = {
            'english': 'en',
            'hindi': 'hi',
            'malayalam': 'ml',
            'arabic': 'ar'
        }
        
        # Initialize model
        self._initialize_model()
        
        logger.info(f"Whisper transcriber initialized - Model: {self.model_size}, Language: {language}, Device: {self.device}")
    
    def _initialize_model(self) -> bool:
        """Initialize Whisper model"""
        try:
            logger.info(f"Loading Whisper model: {self.model_size}")
            self.model = whisper.load_model(self.model_size, device=self.device)
            
            logger.info(f"Whisper model loaded successfully on {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading Whisper model: {e}")
            return False
    
    @PerformanceUtils.measure_time
    def transcribe_audio_data(self, audio_data: np.ndarray, sample_rate: int = None) -> Optional[Dict[str, Any]]:
        """
        Transcribe speech from audio data
        
        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of audio data
            
        Returns:
            Dictionary with transcription results or None if failed
        """
        if not self.model:
            logger.error("Whisper model not initialized")
            return None
        
        try:
            # Ensure audio is in float32 format and normalized
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            audio_data = AudioUtils.normalize_audio(audio_data)
            
            # Resample to 16kHz if needed (Whisper expects 16kHz)
            if sample_rate and sample_rate != 16000:
                import librosa
                audio_data = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=16000)
            
            # Get Whisper language code
            whisper_lang = self.whisper_language_map.get(self.language, 'en')
            
            # Transcribe with Whisper
            result = self.model.transcribe(
                audio_data,
                language=whisper_lang,
                task='transcribe',
                word_timestamps=True,
                verbose=False
            )
            
            return self._process_whisper_result(result)
            
        except Exception as e:
            logger.error(f"Error in Whisper transcription: {e}")
            return None
    
    @PerformanceUtils.measure_time
    def transcribe_from_file(self, audio_file: str) -> Optional[Dict[str, Any]]:
        """
        Transcribe speech from audio file
        
        Args:
            audio_file: Path to audio file
            
        Returns:
            Dictionary with transcription results or None if failed
        """
        if not self.model:
            logger.error("Whisper model not initialized")
            return None
        
        try:
            # Get Whisper language code
            whisper_lang = self.whisper_language_map.get(self.language, 'en')
            
            # Transcribe with Whisper
            result = self.model.transcribe(
                audio_file,
                language=whisper_lang,
                task='transcribe',
                word_timestamps=True,
                verbose=False
            )
            
            return self._process_whisper_result(result)
            
        except Exception as e:
            logger.error(f"Error transcribing file {audio_file}: {e}")
            return None
    
    def _process_whisper_result(self, result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process Whisper transcription result
        
        Args:
            result: Raw result from Whisper
            
        Returns:
            Processed transcription results
        """
        try:
            if not result or not result.get('text'):
                logger.warning("No transcription result from Whisper")
                return None
            
            # Clean the transcribed text
            text = TextUtils.clean_text(result['text'])
            
            # Process segments for detailed information
            segments = []
            words = []
            
            for segment in result.get('segments', []):
                segment_data = {
                    'id': segment.get('id', 0),
                    'text': TextUtils.clean_text(segment.get('text', '')),
                    'start': segment.get('start', 0.0),
                    'end': segment.get('end', 0.0),
                    'avg_logprob': segment.get('avg_logprob', 0.0),
                    'no_speech_prob': segment.get('no_speech_prob', 0.0)
                }
                segments.append(segment_data)
                
                # Process word-level timestamps if available
                if 'words' in segment:
                    for word_info in segment['words']:
                        word_data = {
                            'word': word_info.get('word', '').strip(),
                            'start': word_info.get('start', 0.0),
                            'end': word_info.get('end', 0.0),
                            'probability': word_info.get('probability', 0.0)
                        }
                        words.append(word_data)
            
            # Calculate overall confidence from segments
            if segments:
                # Use average of segment probabilities as confidence
                avg_logprob = np.mean([seg['avg_logprob'] for seg in segments])
                # Convert log probability to confidence (approximate)
                confidence = min(1.0, max(0.0, np.exp(avg_logprob)))
            else:
                confidence = 0.8  # Default confidence for Whisper
            
            # Create result dictionary
            result_dict = {
                'timestamp': time.time(),
                'language': self.language,
                'language_code': self.language_code,
                'engine': 'whisper',
                'model_size': self.model_size,
                'text': text,
                'confidence': confidence,
                'segments': segments,
                'words': words,
                'detected_language': result.get('language', ''),
                'language_probability': result.get('language_probability', 0.0)
            }
            
            # Check if it's a movement command
            command = VoiceConfig.is_movement_command(text, self.language)
            if command:
                result_dict['movement_command'] = command
                logger.info(f"Movement command detected: {command}")
            
            logger.info(f"Whisper transcription: {text} (confidence: {confidence:.2f})")
            
            return result_dict
            
        except Exception as e:
            logger.error(f"Error processing Whisper result: {e}")
            return None
    
    def transcribe_with_translation(self, audio_data: np.ndarray, target_language: str = 'english') -> Optional[Dict[str, Any]]:
        """
        Transcribe and translate audio to target language
        
        Args:
            audio_data: Audio data as numpy array
            target_language: Target language for translation
            
        Returns:
            Dictionary with transcription and translation results
        """
        if not self.model:
            logger.error("Whisper model not initialized")
            return None
        
        try:
            # Normalize audio
            audio_data = AudioUtils.normalize_audio(audio_data.astype(np.float32))
            
            # Get source language
            source_lang = self.whisper_language_map.get(self.language, 'en')
            target_lang = self.whisper_language_map.get(target_language, 'en')
            
            # Transcribe in original language
            transcribe_result = self.model.transcribe(
                audio_data,
                language=source_lang,
                task='transcribe',
                word_timestamps=True,
                verbose=False
            )
            
            # Translate to target language if different
            if source_lang != target_lang:
                translate_result = self.model.transcribe(
                    audio_data,
                    task='translate',  # This translates to English
                    word_timestamps=True,
                    verbose=False
                )
                
                result_dict = {
                    'timestamp': time.time(),
                    'source_language': self.language,
                    'target_language': target_language,
                    'engine': 'whisper_translate',
                    'original_text': TextUtils.clean_text(transcribe_result['text']),
                    'translated_text': TextUtils.clean_text(translate_result['text']),
                    'original_segments': transcribe_result.get('segments', []),
                    'translated_segments': translate_result.get('segments', [])
                }
            else:
                # Same language, just return transcription
                result_dict = self._process_whisper_result(transcribe_result)
                result_dict['translated_text'] = result_dict['text']
            
            return result_dict
            
        except Exception as e:
            logger.error(f"Error in transcription with translation: {e}")
            return None
    
    def detect_language(self, audio_data: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        Detect the language of the audio
        
        Args:
            audio_data: Audio data as numpy array
            
        Returns:
            Dictionary with language detection results
        """
        if not self.model:
            logger.error("Whisper model not initialized")
            return None
        
        try:
            # Normalize audio
            audio_data = AudioUtils.normalize_audio(audio_data.astype(np.float32))
            
            # Use Whisper's language detection
            # Load audio into Whisper format
            audio_tensor = torch.from_numpy(audio_data).to(self.device)
            
            # Detect language
            _, probs = self.model.detect_language(audio_tensor)
            
            # Get top languages
            top_languages = sorted(probs.items(), key=lambda x: x[1], reverse=True)[:5]
            
            result = {
                'timestamp': time.time(),
                'engine': 'whisper_language_detection',
                'detected_language': top_languages[0][0],
                'confidence': top_languages[0][1],
                'top_languages': top_languages
            }
            
            logger.info(f"Language detected: {result['detected_language']} (confidence: {result['confidence']:.2f})")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in language detection: {e}")
            return None
    
    def set_language(self, language: str) -> bool:
        """
        Change the transcription language
        
        Args:
            language: New language to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if language not in self.whisper_language_map:
                logger.error(f"Language {language} not supported by Whisper")
                return False
            
            self.language = language
            self.language_code = VoiceConfig.get_language_code(language)
            
            logger.info(f"Language changed to: {language}")
            return True
            
        except Exception as e:
            logger.error(f"Error changing language: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        try:
            if not self.model:
                return {'status': 'not_loaded'}
            
            return {
                'status': 'loaded',
                'model_size': self.model_size,
                'language': self.language,
                'device': self.device,
                'supported_languages': list(self.whisper_language_map.keys())
            }
            
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def is_available(self) -> bool:
        """Check if Whisper model is available and ready"""
        return self.model is not None
    
    def benchmark_model(self, duration: float = 5.0) -> Dict[str, Any]:
        """
        Benchmark the model performance
        
        Args:
            duration: Duration of test audio in seconds
            
        Returns:
            Benchmark results
        """
        try:
            if not self.model:
                return {'error': 'Model not loaded'}
            
            # Generate test audio
            sample_rate = 16000
            samples = int(duration * sample_rate)
            test_audio = np.random.normal(0, 0.1, samples).astype(np.float32)
            
            # Measure transcription time
            start_time = time.time()
            result = self.transcribe_audio_data(test_audio, sample_rate)
            end_time = time.time()
            
            processing_time = end_time - start_time
            real_time_factor = processing_time / duration
            
            benchmark_result = {
                'model_size': self.model_size,
                'device': self.device,
                'audio_duration': duration,
                'processing_time': processing_time,
                'real_time_factor': real_time_factor,
                'performance': 'real-time' if real_time_factor < 1.0 else 'slower-than-real-time'
            }
            
            logger.info(f"Whisper benchmark - RTF: {real_time_factor:.2f}")
            
            return benchmark_result
            
        except Exception as e:
            logger.error(f"Error in model benchmark: {e}")
            return {'error': str(e)}
