"""
Utility functions for STEM-Xpert Voice Recognition System
"""

import os
import logging
import time
import wave
import json
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
import numpy as np
import soundfile as sf
from pydub import AudioSegment

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioUtils:
    """Utility class for audio processing operations"""
    
    @staticmethod
    def save_audio(audio_data: np.ndarray, filename: str, sample_rate: int = 16000) -> bool:
        """Save audio data to file"""
        try:
            sf.write(filename, audio_data, sample_rate)
            logger.info(f"Audio saved to {filename}")
            return True
        except Exception as e:
            logger.error(f"Error saving audio: {e}")
            return False
    
    @staticmethod
    def load_audio(filename: str) -> Tuple[Optional[np.ndarray], Optional[int]]:
        """Load audio file and return data with sample rate"""
        try:
            audio_data, sample_rate = sf.read(filename)
            logger.info(f"Audio loaded from {filename}")
            return audio_data, sample_rate
        except Exception as e:
            logger.error(f"Error loading audio: {e}")
            return None, None
    
    @staticmethod
    def convert_audio_format(input_file: str, output_file: str, target_format: str = "wav") -> bool:
        """Convert audio file to target format"""
        try:
            audio = AudioSegment.from_file(input_file)
            audio.export(output_file, format=target_format)
            logger.info(f"Audio converted from {input_file} to {output_file}")
            return True
        except Exception as e:
            logger.error(f"Error converting audio: {e}")
            return False
    
    @staticmethod
    def normalize_audio(audio_data: np.ndarray) -> np.ndarray:
        """Normalize audio data to prevent clipping"""
        try:
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                return audio_data / max_val
            return audio_data
        except Exception as e:
            logger.error(f"Error normalizing audio: {e}")
            return audio_data
    
    @staticmethod
    def apply_noise_reduction(audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """Apply basic noise reduction to audio data"""
        try:
            # Simple high-pass filter to remove low-frequency noise
            from scipy.signal import butter, filtfilt
            
            nyquist = sample_rate / 2
            low_cutoff = 300 / nyquist  # Remove frequencies below 300Hz
            
            b, a = butter(4, low_cutoff, btype='high')
            filtered_audio = filtfilt(b, a, audio_data)
            
            return filtered_audio
        except Exception as e:
            logger.error(f"Error applying noise reduction: {e}")
            return audio_data

class TextUtils:
    """Utility class for text processing operations"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""
        
        # Remove extra whitespace and normalize
        text = ' '.join(text.split())
        
        # Remove special characters but keep basic punctuation
        import re
        text = re.sub(r'[^\w\s\.\,\?\!\-]', '', text)
        
        return text.strip()
    
    @staticmethod
    def extract_keywords(text: str, language: str = 'english') -> List[str]:
        """Extract keywords from text using NLTK"""
        try:
            import nltk
            from nltk.corpus import stopwords
            from nltk.tokenize import word_tokenize
            
            # Download required NLTK data if not present
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt')
            
            try:
                nltk.data.find('corpora/stopwords')
            except LookupError:
                nltk.download('stopwords')
            
            # Tokenize and remove stopwords
            tokens = word_tokenize(text.lower())
            stop_words = set(stopwords.words(language))
            keywords = [word for word in tokens if word.isalpha() and word not in stop_words]
            
            return keywords
        except Exception as e:
            logger.error(f"Error extracting keywords: {e}")
            return text.split()
    
    @staticmethod
    def calculate_similarity(text1: str, text2: str) -> float:
        """Calculate similarity between two texts"""
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.metrics.pairwise import cosine_similarity
            
            vectorizer = TfidfVectorizer()
            tfidf_matrix = vectorizer.fit_transform([text1, text2])
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            
            return similarity
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0

class FileUtils:
    """Utility class for file operations"""
    
    @staticmethod
    def ensure_directory(directory: str) -> bool:
        """Ensure directory exists, create if it doesn't"""
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Error creating directory {directory}: {e}")
            return False
    
    @staticmethod
    def save_json(data: Dict[Any, Any], filename: str) -> bool:
        """Save data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Data saved to {filename}")
            return True
        except Exception as e:
            logger.error(f"Error saving JSON: {e}")
            return False
    
    @staticmethod
    def load_json(filename: str) -> Optional[Dict[Any, Any]]:
        """Load data from JSON file"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"Data loaded from {filename}")
            return data
        except Exception as e:
            logger.error(f"Error loading JSON: {e}")
            return None

class PerformanceUtils:
    """Utility class for performance monitoring"""
    
    @staticmethod
    def measure_time(func):
        """Decorator to measure function execution time"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            logger.info(f"{func.__name__} executed in {execution_time:.4f} seconds")
            return result
        return wrapper
    
    @staticmethod
    def log_memory_usage():
        """Log current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            logger.info(f"Memory usage: {memory_info.rss / 1024 / 1024:.2f} MB")
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")

class ValidationUtils:
    """Utility class for data validation"""
    
    @staticmethod
    def validate_audio_file(filename: str) -> bool:
        """Validate if file is a valid audio file"""
        try:
            audio_data, sample_rate = AudioUtils.load_audio(filename)
            return audio_data is not None and sample_rate is not None
        except Exception:
            return False
    
    @staticmethod
    def validate_language_code(language_code: str) -> bool:
        """Validate if language code is supported"""
        from .config import VoiceConfig
        return language_code in VoiceConfig.SUPPORTED_LANGUAGES.values()
    
    @staticmethod
    def validate_confidence_score(confidence: float) -> bool:
        """Validate confidence score is within valid range"""
        return 0.0 <= confidence <= 1.0
