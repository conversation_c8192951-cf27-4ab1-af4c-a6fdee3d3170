"""
Configuration settings for STEM-Xpert Voice Recognition System
"""

import os
from typing import Dict, List

class VoiceConfig:
    """Configuration class for voice recognition settings"""
    
    # Supported languages for the STEM-Xpert robot
    SUPPORTED_LANGUAGES = {
        'english': 'en-US',
        'hindi': 'hi-IN', 
        'malayalam': 'ml-IN',
        'arabic': 'ar-SA'
    }
    
    # Default language
    DEFAULT_LANGUAGE = 'english'
    
    # Audio settings
    AUDIO_SETTINGS = {
        'sample_rate': 16000,
        'chunk_size': 1024,
        'channels': 1,
        'format': 'int16',
        'timeout': 5,
        'phrase_timeout': 1
    }
    
    # Speech recognition confidence thresholds
    CONFIDENCE_THRESHOLDS = {
        'high': 0.8,
        'medium': 0.6,
        'low': 0.4
    }
    
    # Robot movement commands
    MOVEMENT_COMMANDS = {
        'english': {
            'move_forward': ['move forward', 'go forward', 'move front', 'go ahead'],
            'move_backward': ['move backward', 'go backward', 'move back', 'go back'],
            'move_left': ['move left', 'turn left', 'go left'],
            'move_right': ['move right', 'turn right', 'go right'],
            'stop': ['stop', 'halt', 'freeze'],
            'say_hi': ['say hi', 'say hello', 'greet', 'hello'],
            'wave_hand': ['wave hand', 'wave', 'wave hello'],
            'rotate_head': ['rotate head', 'turn head', 'look around'],
            'shake_hand': ['shake hand', 'handshake', 'shake hands']
        },
        'hindi': {
            'move_forward': ['आगे बढ़ो', 'आगे जाओ'],
            'move_backward': ['पीछे जाओ', 'वापस जाओ'],
            'move_left': ['बाएं जाओ', 'बाएं मुड़ो'],
            'move_right': ['दाएं जाओ', 'दाएं मुड़ो'],
            'stop': ['रुको', 'ठहरो'],
            'say_hi': ['नमस्ते कहो', 'हैलो कहो'],
            'wave_hand': ['हाथ हिलाओ'],
            'shake_hand': ['हाथ मिलाओ']
        }
    }
    
    # API Keys (to be set via environment variables)
    GOOGLE_SPEECH_API_KEY = os.getenv('GOOGLE_SPEECH_API_KEY', '')
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    
    # Vosk model paths
    VOSK_MODEL_PATHS = {
        'english': 'models/vosk-model-en-us-0.22',
        'hindi': 'models/vosk-model-hi-0.22',
        'arabic': 'models/vosk-model-ar-0.22'
    }
    
    # Whisper model settings
    WHISPER_MODEL = 'base'  # Options: tiny, base, small, medium, large
    
    # PocketSphinx settings
    POCKETSPHINX_CONFIG = {
        'hmm': 'models/pocketsphinx/en-us',
        'lm': 'models/pocketsphinx/en-us.lm.bin',
        'dict': 'models/pocketsphinx/cmudict-en-us.dict'
    }
    
    # NLP settings
    NLP_CONFIG = {
        'intent_confidence_threshold': 0.7,
        'entity_confidence_threshold': 0.6,
        'max_tokens': 512
    }
    
    # Fallback priority order
    RECOGNITION_PRIORITY = [
        'google_speech',  # Primary - cloud-based, high accuracy
        'whisper',        # Secondary - high accuracy, can work offline
        'vosk',          # Tertiary - fast offline processing
        'speech_recognition',  # Quaternary - general purpose
        'pocketsphinx'   # Last resort - lightweight
    ]
    
    # Logging configuration
    LOGGING_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file': 'logs/voice_recognition.log'
    }

    @classmethod
    def get_language_code(cls, language: str) -> str:
        """Get language code for the specified language"""
        return cls.SUPPORTED_LANGUAGES.get(language.lower(), cls.SUPPORTED_LANGUAGES[cls.DEFAULT_LANGUAGE])
    
    @classmethod
    def get_movement_commands(cls, language: str) -> Dict[str, List[str]]:
        """Get movement commands for the specified language"""
        return cls.MOVEMENT_COMMANDS.get(language.lower(), cls.MOVEMENT_COMMANDS[cls.DEFAULT_LANGUAGE])
    
    @classmethod
    def is_movement_command(cls, text: str, language: str = None) -> str:
        """Check if text is a movement command and return the command type"""
        if language is None:
            language = cls.DEFAULT_LANGUAGE
            
        commands = cls.get_movement_commands(language)
        text_lower = text.lower().strip()
        
        for command_type, phrases in commands.items():
            for phrase in phrases:
                if phrase in text_lower:
                    return command_type
        return None
