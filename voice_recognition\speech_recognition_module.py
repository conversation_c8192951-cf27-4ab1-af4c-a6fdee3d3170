"""
SpeechRecognition Library Implementation for STEM-Xpert Robot
Primary speech recognition module using the SpeechRecognition library
"""

import speech_recognition as sr
import logging
import time
from typing import Optional, Dict, Any, List
import threading
from .config import VoiceConfig
from .utils import AudioUtils, TextUtils, PerformanceUtils

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SpeechRecognitionModule:
    """
    Primary speech recognition module using SpeechRecognition library
    Supports multiple languages and recognition engines
    """
    
    def __init__(self, language: str = 'english'):
        """Initialize the speech recognition module"""
        self.language = language
        self.language_code = VoiceConfig.get_language_code(language)
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.is_listening = False
        self.recognition_thread = None
        
        # Configure recognizer settings
        self.recognizer.energy_threshold = 300
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8
        self.recognizer.operation_timeout = None
        self.recognizer.phrase_threshold = 0.3
        self.recognizer.non_speaking_duration = 0.8
        
        logger.info(f"SpeechRecognition module initialized for language: {language}")
    
    def initialize_microphone(self) -> bool:
        """Initialize microphone for audio input"""
        try:
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            with self.microphone as source:
                logger.info("Adjusting for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
                logger.info(f"Energy threshold set to: {self.recognizer.energy_threshold}")
            
            return True
        except Exception as e:
            logger.error(f"Error initializing microphone: {e}")
            return False
    
    @PerformanceUtils.measure_time
    def recognize_from_microphone(self, timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from microphone input
        
        Args:
            timeout: Maximum time to wait for speech
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.microphone:
            if not self.initialize_microphone():
                return None
        
        try:
            logger.info("Listening for speech...")
            
            with self.microphone as source:
                # Listen for audio with timeout
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout,
                    phrase_time_limit=VoiceConfig.AUDIO_SETTINGS['phrase_timeout']
                )
            
            return self._process_audio(audio)
            
        except sr.WaitTimeoutError:
            logger.warning("Listening timeout - no speech detected")
            return None
        except Exception as e:
            logger.error(f"Error during microphone recognition: {e}")
            return None
    
    @PerformanceUtils.measure_time
    def recognize_from_file(self, audio_file: str) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from audio file
        
        Args:
            audio_file: Path to audio file
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        try:
            with sr.AudioFile(audio_file) as source:
                # Record the audio data
                audio = self.recognizer.record(source)
            
            return self._process_audio(audio)
            
        except Exception as e:
            logger.error(f"Error recognizing from file {audio_file}: {e}")
            return None
    
    def _process_audio(self, audio: sr.AudioData) -> Optional[Dict[str, Any]]:
        """
        Process audio data using multiple recognition engines
        
        Args:
            audio: AudioData object from speech_recognition
            
        Returns:
            Dictionary with recognition results
        """
        results = {
            'timestamp': time.time(),
            'language': self.language,
            'language_code': self.language_code,
            'engines': {}
        }
        
        # Try Google Speech Recognition (primary)
        try:
            text = self.recognizer.recognize_google(
                audio, 
                language=self.language_code,
                show_all=False
            )
            
            if text:
                results['engines']['google'] = {
                    'text': TextUtils.clean_text(text),
                    'confidence': 0.9,  # Google doesn't provide confidence in free tier
                    'success': True
                }
                logger.info(f"Google recognition: {text}")
        except sr.UnknownValueError:
            logger.warning("Google Speech Recognition could not understand audio")
            results['engines']['google'] = {'success': False, 'error': 'UnknownValue'}
        except sr.RequestError as e:
            logger.error(f"Google Speech Recognition error: {e}")
            results['engines']['google'] = {'success': False, 'error': str(e)}
        
        # Try Sphinx (offline backup)
        try:
            text = self.recognizer.recognize_sphinx(audio)
            if text:
                results['engines']['sphinx'] = {
                    'text': TextUtils.clean_text(text),
                    'confidence': 0.7,  # Estimated confidence
                    'success': True
                }
                logger.info(f"Sphinx recognition: {text}")
        except sr.UnknownValueError:
            logger.warning("Sphinx could not understand audio")
            results['engines']['sphinx'] = {'success': False, 'error': 'UnknownValue'}
        except sr.RequestError as e:
            logger.error(f"Sphinx error: {e}")
            results['engines']['sphinx'] = {'success': False, 'error': str(e)}
        
        # Determine best result
        best_result = self._get_best_result(results['engines'])
        if best_result:
            results['best_text'] = best_result['text']
            results['best_confidence'] = best_result['confidence']
            results['best_engine'] = best_result['engine']
            
            # Check if it's a movement command
            command = VoiceConfig.is_movement_command(best_result['text'], self.language)
            if command:
                results['movement_command'] = command
                logger.info(f"Movement command detected: {command}")
        
        return results
    
    def _get_best_result(self, engines: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Select the best recognition result from multiple engines
        
        Args:
            engines: Dictionary of engine results
            
        Returns:
            Best result dictionary or None
        """
        # Priority order: Google > Sphinx
        priority_order = ['google', 'sphinx']
        
        for engine in priority_order:
            if engine in engines and engines[engine].get('success', False):
                result = engines[engine].copy()
                result['engine'] = engine
                return result
        
        return None
    
    def start_continuous_listening(self, callback_function=None) -> bool:
        """
        Start continuous listening in a separate thread
        
        Args:
            callback_function: Function to call with recognition results
            
        Returns:
            True if started successfully, False otherwise
        """
        if self.is_listening:
            logger.warning("Already listening")
            return False
        
        if not self.microphone:
            if not self.initialize_microphone():
                return False
        
        self.is_listening = True
        self.recognition_thread = threading.Thread(
            target=self._continuous_listen_worker,
            args=(callback_function,)
        )
        self.recognition_thread.daemon = True
        self.recognition_thread.start()
        
        logger.info("Started continuous listening")
        return True
    
    def stop_continuous_listening(self):
        """Stop continuous listening"""
        self.is_listening = False
        if self.recognition_thread:
            self.recognition_thread.join(timeout=2)
        logger.info("Stopped continuous listening")
    
    def _continuous_listen_worker(self, callback_function=None):
        """Worker function for continuous listening"""
        while self.is_listening:
            try:
                result = self.recognize_from_microphone(timeout=1.0)
                if result and result.get('best_text'):
                    if callback_function:
                        callback_function(result)
                    else:
                        logger.info(f"Recognized: {result['best_text']}")
            except Exception as e:
                logger.error(f"Error in continuous listening: {e}")
                time.sleep(0.1)
    
    def set_language(self, language: str) -> bool:
        """
        Change the recognition language
        
        Args:
            language: New language to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.language = language
            self.language_code = VoiceConfig.get_language_code(language)
            logger.info(f"Language changed to: {language} ({self.language_code})")
            return True
        except Exception as e:
            logger.error(f"Error changing language: {e}")
            return False
    
    def get_available_microphones(self) -> List[str]:
        """Get list of available microphones"""
        try:
            mic_list = sr.Microphone.list_microphone_names()
            logger.info(f"Available microphones: {mic_list}")
            return mic_list
        except Exception as e:
            logger.error(f"Error getting microphones: {e}")
            return []
    
    def test_microphone(self) -> bool:
        """Test microphone functionality"""
        try:
            if not self.microphone:
                if not self.initialize_microphone():
                    return False
            
            logger.info("Testing microphone - say something...")
            result = self.recognize_from_microphone(timeout=3.0)
            
            if result and result.get('best_text'):
                logger.info(f"Microphone test successful: {result['best_text']}")
                return True
            else:
                logger.warning("Microphone test failed - no speech detected")
                return False
                
        except Exception as e:
            logger.error(f"Microphone test error: {e}")
            return False
