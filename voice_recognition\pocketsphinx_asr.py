"""
PocketSphinx ASR Implementation for STEM-Xpert Robot
Lightweight offline speech recognition using PocketSphinx
"""

import logging
import time
import os
from typing import Optional, Dict, Any, List
import numpy as np
from pocketsphinx import LiveSpeech, get_model_path, get_data_path
import speech_recognition as sr
from .config import VoiceConfig
from .utils import AudioUtils, TextUtils, PerformanceUtils

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PocketSphinxASR:
    """
    PocketSphinx-based Automatic Speech Recognition
    Provides lightweight, offline speech recognition
    """
    
    def __init__(self, language: str = 'english'):
        """
        Initialize PocketSphinx ASR
        
        Args:
            language: Target language for recognition (currently supports English)
        """
        self.language = language
        self.language_code = VoiceConfig.get_language_code(language)
        self.recognizer = None
        self.live_speech = None
        
        # PocketSphinx configuration
        self.config = self._get_default_config()
        
        # Initialize recognizer
        self._initialize_recognizer()
        
        logger.info(f"PocketSphinx ASR initialized for language: {language}")
    
    def _get_default_config(self) -> Dict[str, str]:
        """Get default PocketSphinx configuration"""
        try:
            model_path = get_model_path()
            data_path = get_data_path()
            
            config = {
                'hmm': os.path.join(model_path, 'en-us'),
                'lm': os.path.join(model_path, 'en-us.lm.bin'),
                'dict': os.path.join(data_path, 'cmudict-en-us.dict'),
                'verbose': False,
                'audio_device': None,
                'sampling_rate': VoiceConfig.AUDIO_SETTINGS['sample_rate'],
                'buffer_size': VoiceConfig.AUDIO_SETTINGS['chunk_size']
            }
            
            # Use custom config if available
            custom_config = VoiceConfig.POCKETSPHINX_CONFIG
            if custom_config:
                config.update(custom_config)
            
            return config
            
        except Exception as e:
            logger.error(f"Error getting PocketSphinx config: {e}")
            return {}
    
    def _initialize_recognizer(self) -> bool:
        """Initialize PocketSphinx recognizer"""
        try:
            # Initialize speech_recognition with PocketSphinx
            self.recognizer = sr.Recognizer()
            
            # Configure recognizer settings
            self.recognizer.energy_threshold = 300
            self.recognizer.dynamic_energy_threshold = True
            self.recognizer.pause_threshold = 0.8
            
            logger.info("PocketSphinx recognizer initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing PocketSphinx recognizer: {e}")
            return False
    
    @PerformanceUtils.measure_time
    def recognize_audio_data(self, audio_data: np.ndarray, sample_rate: int = None) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from audio data
        
        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of audio data
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.recognizer:
            logger.error("PocketSphinx recognizer not initialized")
            return None
        
        try:
            # Convert numpy array to AudioData format
            if sample_rate is None:
                sample_rate = VoiceConfig.AUDIO_SETTINGS['sample_rate']
            
            # Ensure audio is in correct format
            if audio_data.dtype != np.int16:
                audio_data = (AudioUtils.normalize_audio(audio_data) * 32767).astype(np.int16)
            
            # Convert to bytes
            audio_bytes = audio_data.tobytes()
            
            # Create AudioData object
            audio = sr.AudioData(audio_bytes, sample_rate, 2)  # 2 bytes per sample for int16
            
            return self._process_audio(audio)
            
        except Exception as e:
            logger.error(f"Error in PocketSphinx recognition: {e}")
            return None
    
    @PerformanceUtils.measure_time
    def recognize_from_file(self, audio_file: str) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from audio file
        
        Args:
            audio_file: Path to audio file
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.recognizer:
            logger.error("PocketSphinx recognizer not initialized")
            return None
        
        try:
            with sr.AudioFile(audio_file) as source:
                audio = self.recognizer.record(source)
            
            return self._process_audio(audio)
            
        except Exception as e:
            logger.error(f"Error recognizing from file {audio_file}: {e}")
            return None
    
    def recognize_from_microphone(self, timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from microphone
        
        Args:
            timeout: Maximum time to wait for speech
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.recognizer:
            logger.error("PocketSphinx recognizer not initialized")
            return None
        
        try:
            with sr.Microphone() as source:
                # Adjust for ambient noise
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                logger.info("Listening for speech...")
                
                # Listen for audio
                audio = self.recognizer.listen(source, timeout=timeout)
            
            return self._process_audio(audio)
            
        except sr.WaitTimeoutError:
            logger.warning("Listening timeout - no speech detected")
            return None
        except Exception as e:
            logger.error(f"Error during microphone recognition: {e}")
            return None
    
    def _process_audio(self, audio: sr.AudioData) -> Optional[Dict[str, Any]]:
        """
        Process audio data using PocketSphinx
        
        Args:
            audio: AudioData object from speech_recognition
            
        Returns:
            Dictionary with recognition results
        """
        try:
            # Use PocketSphinx for recognition
            text = self.recognizer.recognize_sphinx(audio)
            
            if not text:
                logger.warning("No text recognized by PocketSphinx")
                return None
            
            # Clean the recognized text
            cleaned_text = TextUtils.clean_text(text)
            
            # Create result dictionary
            result = {
                'timestamp': time.time(),
                'language': self.language,
                'language_code': self.language_code,
                'engine': 'pocketsphinx',
                'text': cleaned_text,
                'confidence': 0.6,  # PocketSphinx doesn't provide confidence scores
                'raw_text': text
            }
            
            # Check if it's a movement command
            command = VoiceConfig.is_movement_command(cleaned_text, self.language)
            if command:
                result['movement_command'] = command
                logger.info(f"Movement command detected: {command}")
            
            logger.info(f"PocketSphinx recognition: {cleaned_text}")
            
            return result
            
        except sr.UnknownValueError:
            logger.warning("PocketSphinx could not understand audio")
            return None
        except sr.RequestError as e:
            logger.error(f"PocketSphinx error: {e}")
            return None
        except Exception as e:
            logger.error(f"Error processing audio with PocketSphinx: {e}")
            return None
    
    def start_live_recognition(self, callback_function=None) -> bool:
        """
        Start live speech recognition
        
        Args:
            callback_function: Function to call with recognition results
            
        Returns:
            True if started successfully, False otherwise
        """
        try:
            if not self.config:
                logger.error("PocketSphinx configuration not available")
                return False
            
            # Create LiveSpeech object
            self.live_speech = LiveSpeech(
                verbose=self.config.get('verbose', False),
                sampling_rate=self.config.get('sampling_rate', 16000),
                buffer_size=self.config.get('buffer_size', 2048),
                no_search=False,
                full_utt=False,
                hmm=self.config.get('hmm'),
                lm=self.config.get('lm'),
                dic=self.config.get('dict')
            )
            
            logger.info("Started live PocketSphinx recognition")
            
            # Process live speech
            for phrase in self.live_speech:
                if phrase:
                    text = str(phrase)
                    cleaned_text = TextUtils.clean_text(text)
                    
                    result = {
                        'timestamp': time.time(),
                        'language': self.language,
                        'engine': 'pocketsphinx_live',
                        'text': cleaned_text,
                        'confidence': 0.6
                    }
                    
                    # Check for movement command
                    command = VoiceConfig.is_movement_command(cleaned_text, self.language)
                    if command:
                        result['movement_command'] = command
                    
                    if callback_function:
                        callback_function(result)
                    else:
                        logger.info(f"Live recognition: {cleaned_text}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error in live recognition: {e}")
            return False
    
    def stop_live_recognition(self):
        """Stop live speech recognition"""
        try:
            if self.live_speech:
                # LiveSpeech doesn't have a direct stop method
                # The loop will break when the generator is exhausted
                self.live_speech = None
                logger.info("Stopped live PocketSphinx recognition")
        except Exception as e:
            logger.error(f"Error stopping live recognition: {e}")
    
    def create_custom_grammar(self, grammar_rules: List[str], grammar_name: str = "custom") -> bool:
        """
        Create custom grammar for better recognition of specific phrases
        
        Args:
            grammar_rules: List of grammar rules
            grammar_name: Name for the grammar
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create grammar file content
            grammar_content = f"#JSGF V1.0;\ngrammar {grammar_name};\n"
            
            for i, rule in enumerate(grammar_rules):
                grammar_content += f"public <rule{i}> = {rule};\n"
            
            # Save grammar file
            grammar_file = f"{grammar_name}.gram"
            with open(grammar_file, 'w') as f:
                f.write(grammar_content)
            
            logger.info(f"Custom grammar created: {grammar_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating custom grammar: {e}")
            return False
    
    def add_keywords(self, keywords: List[str], threshold: float = 1e-20) -> bool:
        """
        Add keywords for keyword spotting
        
        Args:
            keywords: List of keywords to spot
            threshold: Detection threshold
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create keyword file
            keyword_file = "keywords.txt"
            with open(keyword_file, 'w') as f:
                for keyword in keywords:
                    f.write(f"{keyword} /{threshold}/\n")
            
            # Update configuration to use keywords
            self.config['kws'] = keyword_file
            
            logger.info(f"Keywords added: {keywords}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding keywords: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the PocketSphinx model"""
        try:
            return {
                'status': 'loaded' if self.recognizer else 'not_loaded',
                'language': self.language,
                'config': self.config,
                'model_path': self.config.get('hmm', 'unknown'),
                'language_model': self.config.get('lm', 'unknown'),
                'dictionary': self.config.get('dict', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def is_available(self) -> bool:
        """Check if PocketSphinx is available and ready"""
        return self.recognizer is not None
    
    def test_recognition(self) -> bool:
        """Test PocketSphinx recognition with microphone"""
        try:
            logger.info("Testing PocketSphinx recognition - say something...")
            result = self.recognize_from_microphone(timeout=3.0)
            
            if result and result.get('text'):
                logger.info(f"PocketSphinx test successful: {result['text']}")
                return True
            else:
                logger.warning("PocketSphinx test failed - no speech detected")
                return False
                
        except Exception as e:
            logger.error(f"PocketSphinx test error: {e}")
            return False
    
    def optimize_for_commands(self, command_list: List[str]) -> bool:
        """
        Optimize recognition for specific command words
        
        Args:
            command_list: List of command words to optimize for
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create a custom dictionary with command words
            custom_dict = "commands.dict"
            
            with open(custom_dict, 'w') as f:
                for command in command_list:
                    # Simple phonetic representation (this would need proper phoneme mapping)
                    words = command.lower().split()
                    for word in words:
                        f.write(f"{word.upper()} {word.upper()}\n")
            
            # Update configuration
            self.config['dict'] = custom_dict
            
            logger.info(f"Optimized for commands: {command_list}")
            return True
            
        except Exception as e:
            logger.error(f"Error optimizing for commands: {e}")
            return False
