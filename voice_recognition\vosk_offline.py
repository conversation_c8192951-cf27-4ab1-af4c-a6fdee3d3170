"""
Vosk Offline Speech Recognition for STEM-Xpert Robot
Fast offline speech recognition using Vosk models
"""

import json
import logging
import time
import wave
from typing import Optional, Dict, Any, List
import numpy as np
import vosk
from .config import VoiceConfig
from .utils import AudioUtils, TextUtils, PerformanceUtils

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoskOfflineRecognizer:
    """
    Vosk-based offline speech recognition
    Provides fast, offline speech recognition capabilities
    """
    
    def __init__(self, language: str = 'english', model_path: str = None):
        """
        Initialize Vosk recognizer
        
        Args:
            language: Target language for recognition
            model_path: Path to Vosk model directory
        """
        self.language = language
        self.language_code = VoiceConfig.get_language_code(language)
        self.model_path = model_path or VoiceConfig.VOSK_MODEL_PATHS.get(language)
        self.model = None
        self.recognizer = None
        
        # Initialize model
        self._initialize_model()
        
        logger.info(f"Vosk offline recognizer initialized for language: {language}")
    
    def _initialize_model(self) -> bool:
        """Initialize Vosk model and recognizer"""
        try:
            if not self.model_path:
                logger.error(f"No model path specified for language: {self.language}")
                return False
            
            # Check if model exists
            import os
            if not os.path.exists(self.model_path):
                logger.error(f"Vosk model not found at: {self.model_path}")
                logger.info("Please download the appropriate Vosk model")
                return False
            
            # Load model
            self.model = vosk.Model(self.model_path)
            
            # Create recognizer with default sample rate
            sample_rate = VoiceConfig.AUDIO_SETTINGS['sample_rate']
            self.recognizer = vosk.KaldiRecognizer(self.model, sample_rate)
            
            # Enable word-level timestamps
            self.recognizer.SetWords(True)
            
            logger.info(f"Vosk model loaded from: {self.model_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Vosk model: {e}")
            return False
    
    @PerformanceUtils.measure_time
    def recognize_audio_data(self, audio_data: np.ndarray, sample_rate: int = None) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from audio data
        
        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of audio data
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.model or not self.recognizer:
            logger.error("Vosk model not initialized")
            return None
        
        try:
            # Ensure correct sample rate
            if sample_rate is None:
                sample_rate = VoiceConfig.AUDIO_SETTINGS['sample_rate']
            
            # Convert to int16 format expected by Vosk
            if audio_data.dtype != np.int16:
                audio_data = (AudioUtils.normalize_audio(audio_data) * 32767).astype(np.int16)
            
            # Convert to bytes
            audio_bytes = audio_data.tobytes()
            
            # Process audio in chunks
            chunk_size = 4000  # Process in 4KB chunks
            results = []
            
            for i in range(0, len(audio_bytes), chunk_size):
                chunk = audio_bytes[i:i + chunk_size]
                
                if self.recognizer.AcceptWaveform(chunk):
                    result = json.loads(self.recognizer.Result())
                    if result.get('text'):
                        results.append(result)
            
            # Get final result
            final_result = json.loads(self.recognizer.FinalResult())
            if final_result.get('text'):
                results.append(final_result)
            
            return self._process_results(results)
            
        except Exception as e:
            logger.error(f"Error in Vosk recognition: {e}")
            return None
    
    @PerformanceUtils.measure_time
    def recognize_from_file(self, audio_file: str) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from audio file
        
        Args:
            audio_file: Path to audio file
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.model or not self.recognizer:
            logger.error("Vosk model not initialized")
            return None
        
        try:
            # Load audio file
            audio_data, sample_rate = AudioUtils.load_audio(audio_file)
            if audio_data is None:
                return None
            
            return self.recognize_audio_data(audio_data, sample_rate)
            
        except Exception as e:
            logger.error(f"Error recognizing from file {audio_file}: {e}")
            return None
    
    def recognize_from_wav_file(self, wav_file: str) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from WAV file using Vosk's native format
        
        Args:
            wav_file: Path to WAV file
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.model or not self.recognizer:
            logger.error("Vosk model not initialized")
            return None
        
        try:
            # Open WAV file
            with wave.open(wav_file, 'rb') as wf:
                # Check format
                if wf.getnchannels() != 1:
                    logger.error("Audio file must be mono")
                    return None
                
                if wf.getsampwidth() != 2:
                    logger.error("Audio file must be 16-bit")
                    return None
                
                sample_rate = wf.getframerate()
                if sample_rate != VoiceConfig.AUDIO_SETTINGS['sample_rate']:
                    logger.warning(f"Sample rate mismatch: {sample_rate} vs {VoiceConfig.AUDIO_SETTINGS['sample_rate']}")
                
                # Process audio
                results = []
                
                while True:
                    data = wf.readframes(4000)
                    if len(data) == 0:
                        break
                    
                    if self.recognizer.AcceptWaveform(data):
                        result = json.loads(self.recognizer.Result())
                        if result.get('text'):
                            results.append(result)
                
                # Get final result
                final_result = json.loads(self.recognizer.FinalResult())
                if final_result.get('text'):
                    results.append(final_result)
                
                return self._process_results(results)
            
        except Exception as e:
            logger.error(f"Error recognizing from WAV file {wav_file}: {e}")
            return None
    
    def _process_results(self, results: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Process Vosk recognition results
        
        Args:
            results: List of recognition results from Vosk
            
        Returns:
            Processed recognition results
        """
        try:
            if not results:
                logger.warning("No recognition results from Vosk")
                return None
            
            # Combine all text results
            all_text = []
            all_words = []
            total_confidence = 0
            confidence_count = 0
            
            for result in results:
                if result.get('text'):
                    all_text.append(result['text'])
                
                # Process word-level information if available
                if 'result' in result:
                    for word_info in result['result']:
                        word_data = {
                            'word': word_info.get('word', ''),
                            'confidence': word_info.get('conf', 0.0),
                            'start_time': word_info.get('start', 0.0),
                            'end_time': word_info.get('end', 0.0)
                        }
                        all_words.append(word_data)
                        
                        if word_info.get('conf'):
                            total_confidence += word_info['conf']
                            confidence_count += 1
            
            if not all_text:
                return None
            
            # Combine text
            combined_text = ' '.join(all_text)
            cleaned_text = TextUtils.clean_text(combined_text)
            
            # Calculate average confidence
            avg_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.5
            
            # Create result dictionary
            result_dict = {
                'timestamp': time.time(),
                'language': self.language,
                'language_code': self.language_code,
                'engine': 'vosk_offline',
                'text': cleaned_text,
                'confidence': avg_confidence,
                'words': all_words,
                'raw_results': results
            }
            
            # Check if it's a movement command
            command = VoiceConfig.is_movement_command(cleaned_text, self.language)
            if command:
                result_dict['movement_command'] = command
                logger.info(f"Movement command detected: {command}")
            
            logger.info(f"Vosk recognition: {cleaned_text} (confidence: {avg_confidence:.2f})")
            
            return result_dict
            
        except Exception as e:
            logger.error(f"Error processing Vosk results: {e}")
            return None
    
    def recognize_streaming(self, audio_generator) -> Optional[Dict[str, Any]]:
        """
        Perform streaming speech recognition
        
        Args:
            audio_generator: Generator yielding audio chunks
            
        Returns:
            Dictionary with recognition results
        """
        if not self.model or not self.recognizer:
            logger.error("Vosk model not initialized")
            return None
        
        try:
            results = []
            
            for audio_chunk in audio_generator:
                if self.recognizer.AcceptWaveform(audio_chunk):
                    result = json.loads(self.recognizer.Result())
                    if result.get('text'):
                        results.append(result)
                        # Return immediately for streaming
                        return self._process_results([result])
            
            # Get final result if no intermediate results
            final_result = json.loads(self.recognizer.FinalResult())
            if final_result.get('text'):
                results.append(final_result)
            
            return self._process_results(results)
            
        except Exception as e:
            logger.error(f"Error in streaming recognition: {e}")
            return None
    
    def set_language(self, language: str, model_path: str = None) -> bool:
        """
        Change the recognition language
        
        Args:
            language: New language to set
            model_path: Path to new model (optional)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.language = language
            self.language_code = VoiceConfig.get_language_code(language)
            
            if model_path:
                self.model_path = model_path
            else:
                self.model_path = VoiceConfig.VOSK_MODEL_PATHS.get(language)
            
            # Reinitialize model
            success = self._initialize_model()
            
            if success:
                logger.info(f"Language changed to: {language}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error changing language: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        try:
            if not self.model:
                return {'status': 'not_loaded'}
            
            return {
                'status': 'loaded',
                'language': self.language,
                'model_path': self.model_path,
                'sample_rate': VoiceConfig.AUDIO_SETTINGS['sample_rate']
            }
            
        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def is_available(self) -> bool:
        """Check if Vosk recognizer is available and ready"""
        return self.model is not None and self.recognizer is not None
