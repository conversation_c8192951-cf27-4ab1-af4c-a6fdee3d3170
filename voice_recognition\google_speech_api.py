"""
Google Speech API Implementation for STEM-Xpert Robot
Cloud-based speech recognition with high accuracy
"""

import logging
import time
import io
from typing import Optional, Dict, Any, List
import asyncio
from google.cloud import speech
from google.oauth2 import service_account
import numpy as np
from .config import VoiceConfig
from .utils import AudioUtils, TextUtils, PerformanceUtils

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GoogleSpeechAPI:
    """
    Google Cloud Speech-to-Text API implementation
    Provides high-accuracy cloud-based speech recognition
    """
    
    def __init__(self, language: str = 'english', credentials_path: str = None):
        """
        Initialize Google Speech API client
        
        Args:
            language: Target language for recognition
            credentials_path: Path to Google Cloud service account credentials
        """
        self.language = language
        self.language_code = VoiceConfig.get_language_code(language)
        self.client = None
        self.credentials_path = credentials_path
        
        # Initialize client
        self._initialize_client()
        
        # Configuration for speech recognition
        self.config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=VoiceConfig.AUDIO_SETTINGS['sample_rate'],
            language_code=self.language_code,
            enable_automatic_punctuation=True,
            enable_word_confidence=True,
            enable_word_time_offsets=True,
            model='latest_long',  # Use latest model for better accuracy
            use_enhanced=True,    # Use enhanced model if available
        )
        
        logger.info(f"Google Speech API initialized for language: {language}")
    
    def _initialize_client(self) -> bool:
        """Initialize Google Speech client with credentials"""
        try:
            if self.credentials_path:
                credentials = service_account.Credentials.from_service_account_file(
                    self.credentials_path
                )
                self.client = speech.SpeechClient(credentials=credentials)
            else:
                # Use default credentials (environment variable)
                self.client = speech.SpeechClient()
            
            logger.info("Google Speech client initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Google Speech client: {e}")
            logger.warning("Google Speech API will not be available")
            return False
    
    @PerformanceUtils.measure_time
    def recognize_audio_data(self, audio_data: np.ndarray, sample_rate: int = None) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from audio data
        
        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of audio data
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.client:
            logger.error("Google Speech client not initialized")
            return None
        
        try:
            # Convert numpy array to bytes
            if sample_rate is None:
                sample_rate = VoiceConfig.AUDIO_SETTINGS['sample_rate']
            
            # Ensure audio is in correct format
            audio_data = AudioUtils.normalize_audio(audio_data)
            audio_bytes = (audio_data * 32767).astype(np.int16).tobytes()
            
            # Create audio object
            audio = speech.RecognitionAudio(content=audio_bytes)
            
            # Update config with correct sample rate
            config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=sample_rate,
                language_code=self.language_code,
                enable_automatic_punctuation=True,
                enable_word_confidence=True,
                enable_word_time_offsets=True,
                model='latest_long',
                use_enhanced=True,
            )
            
            # Perform recognition
            response = self.client.recognize(config=config, audio=audio)
            
            return self._process_response(response)
            
        except Exception as e:
            logger.error(f"Error in Google Speech recognition: {e}")
            return None
    
    @PerformanceUtils.measure_time
    def recognize_from_file(self, audio_file: str) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from audio file
        
        Args:
            audio_file: Path to audio file
            
        Returns:
            Dictionary with recognition results or None if failed
        """
        if not self.client:
            logger.error("Google Speech client not initialized")
            return None
        
        try:
            # Load audio file
            audio_data, sample_rate = AudioUtils.load_audio(audio_file)
            if audio_data is None:
                return None
            
            return self.recognize_audio_data(audio_data, sample_rate)
            
        except Exception as e:
            logger.error(f"Error recognizing from file {audio_file}: {e}")
            return None
    
    def _process_response(self, response) -> Optional[Dict[str, Any]]:
        """
        Process Google Speech API response
        
        Args:
            response: Google Speech API response object
            
        Returns:
            Processed recognition results
        """
        try:
            results = {
                'timestamp': time.time(),
                'language': self.language,
                'language_code': self.language_code,
                'engine': 'google_speech_api',
                'alternatives': []
            }
            
            if not response.results:
                logger.warning("No recognition results from Google Speech API")
                return None
            
            # Process all alternatives
            for result in response.results:
                for alternative in result.alternatives:
                    alt_data = {
                        'text': TextUtils.clean_text(alternative.transcript),
                        'confidence': alternative.confidence,
                        'words': []
                    }
                    
                    # Process word-level information
                    if hasattr(alternative, 'words'):
                        for word_info in alternative.words:
                            word_data = {
                                'word': word_info.word,
                                'confidence': word_info.confidence,
                                'start_time': word_info.start_time.total_seconds(),
                                'end_time': word_info.end_time.total_seconds()
                            }
                            alt_data['words'].append(word_data)
                    
                    results['alternatives'].append(alt_data)
            
            # Get best result (highest confidence)
            if results['alternatives']:
                best_alt = max(results['alternatives'], key=lambda x: x['confidence'])
                results['best_text'] = best_alt['text']
                results['best_confidence'] = best_alt['confidence']
                
                # Check if it's a movement command
                command = VoiceConfig.is_movement_command(best_alt['text'], self.language)
                if command:
                    results['movement_command'] = command
                    logger.info(f"Movement command detected: {command}")
                
                logger.info(f"Google Speech recognition: {best_alt['text']} (confidence: {best_alt['confidence']:.2f})")
                
                return results
            
            return None
            
        except Exception as e:
            logger.error(f"Error processing Google Speech response: {e}")
            return None
    
    async def recognize_streaming(self, audio_generator) -> Optional[Dict[str, Any]]:
        """
        Perform streaming speech recognition
        
        Args:
            audio_generator: Generator yielding audio chunks
            
        Returns:
            Dictionary with recognition results
        """
        if not self.client:
            logger.error("Google Speech client not initialized")
            return None
        
        try:
            # Configure streaming recognition
            streaming_config = speech.StreamingRecognitionConfig(
                config=self.config,
                interim_results=True,
                single_utterance=False
            )
            
            # Create audio generator
            audio_requests = (speech.StreamingRecognizeRequest(audio_content=chunk)
                            for chunk in audio_generator)
            
            # Perform streaming recognition
            responses = self.client.streaming_recognize(streaming_config, audio_requests)
            
            final_result = None
            
            for response in responses:
                for result in response.results:
                    if result.is_final:
                        alternative = result.alternatives[0]
                        final_result = {
                            'timestamp': time.time(),
                            'language': self.language,
                            'language_code': self.language_code,
                            'engine': 'google_speech_api_streaming',
                            'text': TextUtils.clean_text(alternative.transcript),
                            'confidence': alternative.confidence,
                            'is_final': True
                        }
                        
                        # Check for movement command
                        command = VoiceConfig.is_movement_command(alternative.transcript, self.language)
                        if command:
                            final_result['movement_command'] = command
                        
                        logger.info(f"Streaming recognition final: {alternative.transcript}")
                        break
                
                if final_result:
                    break
            
            return final_result
            
        except Exception as e:
            logger.error(f"Error in streaming recognition: {e}")
            return None
    
    def recognize_long_audio(self, audio_file: str) -> Optional[Dict[str, Any]]:
        """
        Recognize speech from long audio files using async recognition
        
        Args:
            audio_file: Path to audio file (must be in Google Cloud Storage)
            
        Returns:
            Dictionary with recognition results
        """
        if not self.client:
            logger.error("Google Speech client not initialized")
            return None
        
        try:
            # For long audio files, use async recognition
            audio = speech.RecognitionAudio(uri=audio_file)
            
            # Configure for long audio
            config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=VoiceConfig.AUDIO_SETTINGS['sample_rate'],
                language_code=self.language_code,
                enable_automatic_punctuation=True,
                enable_word_confidence=True,
                enable_word_time_offsets=True,
                model='latest_long',
                use_enhanced=True,
            )
            
            # Start async operation
            operation = self.client.long_running_recognize(config=config, audio=audio)
            
            logger.info("Waiting for long audio recognition to complete...")
            response = operation.result(timeout=300)  # 5 minute timeout
            
            return self._process_response(response)
            
        except Exception as e:
            logger.error(f"Error in long audio recognition: {e}")
            return None
    
    def set_language(self, language: str) -> bool:
        """
        Change the recognition language
        
        Args:
            language: New language to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.language = language
            self.language_code = VoiceConfig.get_language_code(language)
            
            # Update configuration
            self.config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=VoiceConfig.AUDIO_SETTINGS['sample_rate'],
                language_code=self.language_code,
                enable_automatic_punctuation=True,
                enable_word_confidence=True,
                enable_word_time_offsets=True,
                model='latest_long',
                use_enhanced=True,
            )
            
            logger.info(f"Language changed to: {language} ({self.language_code})")
            return True
            
        except Exception as e:
            logger.error(f"Error changing language: {e}")
            return False
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages"""
        return list(VoiceConfig.SUPPORTED_LANGUAGES.keys())
    
    def test_connection(self) -> bool:
        """Test Google Speech API connection"""
        try:
            if not self.client:
                return False
            
            # Create a simple test audio
            test_audio = np.random.normal(0, 0.1, 1600).astype(np.float32)  # 0.1 second of noise
            result = self.recognize_audio_data(test_audio)
            
            logger.info("Google Speech API connection test completed")
            return True
            
        except Exception as e:
            logger.error(f"Google Speech API connection test failed: {e}")
            return False
